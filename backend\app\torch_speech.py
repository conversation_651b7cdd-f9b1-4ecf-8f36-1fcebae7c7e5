import torch
import librosa
import numpy as np
import soundfile as sf
from transformers import Wav2Vec2ForCTC, Wav2Vec2Processor
import logging

# Configure logging
logger = logging.getLogger(__name__)

class TorchSpeechRecognizer:
    """Advanced speech recognition using Wav2Vec2 model from Transformers."""
    
    def __init__(self, model_name="facebook/wav2vec2-base-960h"):
        """
        Initialize the speech recognizer with the specified model.
        
        Args:
            model_name (str): Name of the pretrained model to use
        """
        logger.info(f"Initializing TorchSpeechRecognizer with model: {model_name}")
        try:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"Using device: {self.device}")
            
            # Load model and processor
            self.processor = Wav2Vec2Processor.from_pretrained(model_name)
            self.model = Wav2Vec2ForCTC.from_pretrained(model_name).to(self.device)
            
            logger.info("Model and processor loaded successfully")
        except Exception as e:
            logger.error(f"Error initializing speech recognizer: {e}")
            raise
    
    def load_audio(self, file_path, target_sr=16000):
        """
        Load and preprocess audio file.
        
        Args:
            file_path (str): Path to the audio file
            target_sr (int): Target sample rate for the model
            
        Returns:
            numpy.ndarray: Audio data as float32 numpy array
        """
        try:
            # Load audio file
            logger.info(f"Loading audio file: {file_path}")
            audio, sr = librosa.load(file_path, sr=None)
            
            # Resample if necessary
            if sr != target_sr:
                logger.info(f"Resampling audio from {sr}Hz to {target_sr}Hz")
                audio = librosa.resample(audio, orig_sr=sr, target_sr=target_sr)
            
            return audio
        except Exception as e:
            logger.error(f"Error loading audio: {e}")
            raise
    
    def recognize(self, file_path):
        """
        Recognize speech from an audio file.
        
        Args:
            file_path (str): Path to the audio file
            
        Returns:
            str: Recognized text
        """
        try:
            # Load and preprocess audio
            audio = self.load_audio(file_path)
            
            # Prepare input for the model
            inputs = self.processor(
                audio, 
                sampling_rate=16000, 
                return_tensors="pt", 
                padding=True
            ).to(self.device)
            
            # Perform inference
            logger.info("Running inference with Wav2Vec2 model")
            with torch.no_grad():
                logits = self.model(inputs.input_values).logits
            
            # Decode the predicted IDs to text
            predicted_ids = torch.argmax(logits, dim=-1)
            transcription = self.processor.batch_decode(predicted_ids)[0]
            
            logger.info(f"Transcription result: {transcription}")
            return transcription
        
        except Exception as e:
            logger.error(f"Error recognizing speech: {e}")
            raise

# Create a singleton instance
recognizer = None

def get_recognizer():
    """
    Get or create the speech recognizer instance.
    
    Returns:
        TorchSpeechRecognizer: Speech recognizer instance
    """
    global recognizer
    if recognizer is None:
        recognizer = TorchSpeechRecognizer()
    return recognizer 