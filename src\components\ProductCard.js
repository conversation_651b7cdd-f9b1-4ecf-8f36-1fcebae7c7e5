import React from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const { width } = Dimensions.get("window");

const ProductCard = ({ product, onPress, index }) => {
  return (
    <TouchableOpacity
      style={styles.card}
      onPress={() => onPress(product)}
      activeOpacity={0.9}
    >
      <View style={styles.imageContainer}>
        {index && (
          <View style={styles.indexBadge}>
            <Text style={styles.indexText}>{index}</Text>
          </View>
        )}
        <Image
          source={
            typeof product.imageUrl === "string"
              ? { uri: product.imageUrl }
              : product.imageUrl
          }
          style={styles.image}
          resizeMode='cover'
        />
        <LinearGradient
          colors={["transparent", "rgba(0,0,0,0.7)"]}
          style={styles.imageOverlay}
        />
      </View>

      <View style={styles.infoContainer}>
        <View style={styles.headerRow}>
          <Text style={styles.name} numberOfLines={2}>
            {product.name}
          </Text>
          <View style={styles.priceContainer}>
            <Text style={styles.currency}>GHS</Text>
            <Text style={styles.price}>{product.price.toFixed(2)}</Text>
          </View>
        </View>

        <View style={styles.categoryContainer}>
          <View style={styles.categoryBadge}>
            <Text style={styles.category}>⚽ {product.category}</Text>
          </View>
        </View>

        {index && (
          <View style={styles.voiceHintContainer}>
            <Text style={styles.voiceHint}>🎤 Say "select item {index}"</Text>
          </View>
        )}

        <View style={styles.actionIndicator}>
          <Text style={styles.actionText}>Tap to view details</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff",
    borderRadius: 20,
    marginVertical: 12,
    marginHorizontal: 20,
    shadowColor: "#1B5E20",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
    overflow: "hidden",
    transform: [{ scale: 1 }],
  },
  imageContainer: {
    position: "relative",
    height: 200,
  },
  indexBadge: {
    position: "absolute",
    top: 16,
    left: 16,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "#1B5E20",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 2,
    borderColor: "#fff",
  },
  indexText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  image: {
    width: "100%",
    height: "100%",
  },
  imageOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  infoContainer: {
    padding: 20,
  },
  headerRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  name: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1A1A1A",
    flex: 1,
    marginRight: 12,
    lineHeight: 24,
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  currency: {
    fontSize: 12,
    color: "#666",
    fontWeight: "500",
  },
  price: {
    fontSize: 20,
    color: "#1B5E20",
    fontWeight: "bold",
  },
  categoryContainer: {
    marginBottom: 12,
  },
  categoryBadge: {
    backgroundColor: "#E8F5E8",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: "flex-start",
    borderWidth: 1,
    borderColor: "#1B5E20",
  },
  category: {
    fontSize: 12,
    color: "#1B5E20",
    fontWeight: "600",
    textTransform: "capitalize",
  },
  voiceHintContainer: {
    backgroundColor: "#F0F8FF",
    padding: 8,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: "#4CAF50",
  },
  voiceHint: {
    fontSize: 11,
    color: "#1B5E20",
    fontWeight: "500",
  },
  actionIndicator: {
    alignItems: "center",
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#F0F0F0",
  },
  actionText: {
    fontSize: 12,
    color: "#999",
    fontStyle: "italic",
  },
});

export default ProductCard;
