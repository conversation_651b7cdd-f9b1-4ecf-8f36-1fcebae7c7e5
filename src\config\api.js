/**
 * API configuration settings
 */

// Replace with your actual server IP address or hostname
// For local development with Expo, use your computer's local IP address
// Multiple fallback options for different environments
const getServerIP = () => {
  // Try different IP addresses based on environment
  const possibleIPs = [
    "*************", // Common home network range
    "*************", // Alternative home network range
    "**********", // Alternative network range
    "***********", // Mobile hotspot range
    "localhost", // Fallback for web
  ];

  // For now, use the detected WiFi IP address
  // This was detected by running find-ip.js
  return "***********"; // Your computer's actual WiFi IP address
};

const SERVER_IP = getServerIP();
const SERVER_PORT = 5000;

// API configuration
export const API_CONFIG = {
  baseUrl: `http://${SERVER_IP}:${SERVER_PORT}`,
  endpoints: {
    recognize: "/recognize",
    commands: "/commands",
    health: "/health",
  },
};

export default API_CONFIG;
