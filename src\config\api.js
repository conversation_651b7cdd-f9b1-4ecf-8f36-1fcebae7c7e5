/**
 * API configuration settings
 */

// Replace with your actual server IP address or hostname
// For local development with Expo, use your computer's local IP address
// Use localhost for development, or your actual IP for device testing
const SERVER_IP = "localhost"; // Change to your computer's IP address for device testing
const SERVER_PORT = 5000;

// API configuration
export const API_CONFIG = {
  baseUrl: `http://${SERVER_IP}:${SERVER_PORT}`,
  endpoints: {
    recognize: "/recognize",
    commands: "/commands",
    health: "/health",
  },
};

export default API_CONFIG;
