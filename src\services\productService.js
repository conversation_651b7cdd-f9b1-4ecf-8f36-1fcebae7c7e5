import { firestore } from "../config/firebase";
import {
  collection,
  getDocs,
  doc,
  getDoc,
  query,
  where,
  limit,
} from "firebase/firestore";

const productsCollection = "products";

class ProductService {
  // Search products by query
  async searchProducts(searchTerm) {
    try {
      if (!searchTerm || searchTerm.trim() === "") {
        // If no query, return all products
        const snapshot = await getDocs(
          query(collection(firestore, productsCollection), limit(20))
        );
        return this._processSnapshot(snapshot);
      }

      // Convert query to lowercase for case-insensitive search
      const searchTermLower = searchTerm.toLowerCase();

      // In a real app, you would use Firestore's full-text search capabilities
      // or integrate with a service like Algolia
      // For this MVP, we'll use a simple query on name and category fields
      const q = query(
        collection(firestore, productsCollection),
        where("searchTerms", "array-contains", searchTermLower),
        limit(20)
      );
      const snapshot = await getDocs(q);

      return this._processSnapshot(snapshot);
    } catch (error) {
      console.error("Error searching products:", error);
      // Fall back to mock products
      return this.getMockProducts(searchTerm);
    }
  }

  // Get product by ID
  async getProductById(productId) {
    try {
      const docRef = doc(firestore, productsCollection, productId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      return {
        id: docSnap.id,
        ...docSnap.data(),
      };
    } catch (error) {
      console.error("Error getting product by ID:", error);
      // Fall back to mock products
      const mockProducts = this.getMockProducts();
      return mockProducts.find((p) => p.id === productId);
    }
  }

  // Get featured products
  async getFeaturedProducts() {
    try {
      // Check if Firebase is properly configured
      if (
        firestore._databaseId &&
        firestore._databaseId.projectId !== "YOUR_PROJECT_ID"
      ) {
        const q = query(
          collection(firestore, productsCollection),
          where("featured", "==", true),
          limit(10)
        );
        const snapshot = await getDocs(q);
        const result = this._processSnapshot(snapshot);

        // If no products found in Firestore, fall back to mock data
        if (result.length === 0) {
          throw new Error("No featured products found in Firestore");
        }

        return result;
      } else {
        // Firebase not properly configured, use mock data
        throw new Error("Firebase not properly configured");
      }
    } catch (error) {
      console.error("Error getting featured products:", error);
      // Fall back to mock products
      const mockProducts = this.getMockProducts();
      return mockProducts.filter((p) => p.featured);
    }
  }

  // Helper method to process Firestore snapshot
  _processSnapshot(snapshot) {
    const products = [];

    snapshot.forEach((doc) => {
      products.push({
        id: doc.id,
        ...doc.data(),
      });
    });

    return products;
  }

  // For demo purposes, if Firestore is not set up yet, return mock products
  getMockProducts(query = "") {
    const mockProducts = [
      {
        id: "1",
        name: "FC Barcelona Home Jersey",
        price: 89,
        category: "jerseys",
        description:
          "Official FC Barcelona home jersey with classic blue and red stripes",
        imageUrl: require("../images/barcelonal-jersey.jpeg"),
        featured: true,
        searchTerms: [
          "barcelona",
          "barca",
          "home",
          "jersey",
          "football",
          "soccer",
        ],
      },
      {
        id: "2",
        name: "Real Madrid Home Jersey",
        price: 89,
        category: "jerseys",
        description: "Official Real Madrid home jersey in classic white",
        imageUrl: require("../images/real-madrid-jersey.jpeg"),
        featured: true,
        searchTerms: [
          "real madrid",
          "madrid",
          "home",
          "jersey",
          "football",
          "white",
        ],
      },
      {
        id: "3",
        name: "Liverpool FC Home Jersey",
        price: 85,
        category: "jerseys",
        description: "Official Liverpool FC home jersey in iconic red",
        imageUrl: require("../images/liverpool-jersey.jpeg"),
        featured: false,
        searchTerms: ["liverpool", "lfc", "home", "jersey", "football", "red"],
      },
      {
        id: "4",
        name: "Manchester City Home Jersey",
        price: 89,
        category: "jerseys",
        description: "Official Manchester City home jersey in sky blue",
        imageUrl: require("../images/man-city-jersey.jpeg"),
        featured: false,
        searchTerms: [
          "manchester city",
          "city",
          "home",
          "jersey",
          "football",
          "blue",
        ],
      },
      {
        id: "5",
        name: "Chelsea FC Home Jersey",
        price: 85,
        category: "jerseys",
        description: "Official Chelsea FC home jersey in royal blue",
        imageUrl: require("../images/chelsea-jersey.jpeg"),
        featured: true,
        searchTerms: ["chelsea", "home", "jersey", "football", "blue"],
      },
      {
        id: "6",
        name: "Juventus Home Jersey",
        price: 89,
        category: "jerseys",
        description:
          "Official Juventus home jersey in classic black and white stripes",
        imageUrl: require("../images/juventus-jersey.jpeg"),
        featured: true,
        searchTerms: [
          "juventus",
          "juve",
          "home",
          "jersey",
          "football",
          "black",
          "white",
        ],
      },
      {
        id: "7",
        name: "PSG Home Jersey",
        price: 89,
        category: "jerseys",
        description: "Official Paris Saint-Germain home jersey in navy blue",
        imageUrl: require("../images/psg-jersey.jpeg"),
        featured: true,
        searchTerms: [
          "psg",
          "paris",
          "saint-germain",
          "home",
          "jersey",
          "football",
          "navy",
        ],
      },
      {
        id: "8",
        name: "Portugal National Team Jersey",
        price: 79,
        category: "jerseys",
        description: "Official Portugal national team jersey in red",
        imageUrl: require("../images/portugal-jersey.webp"),
        featured: false,
        searchTerms: [
          "portugal",
          "national",
          "team",
          "jersey",
          "football",
          "red",
        ],
      },
      {
        id: "9",
        name: "FC Barcelona Away Jersey",
        price: 89,
        category: "jerseys",
        description: "Official FC Barcelona away jersey in vibrant colors",
        imageUrl: require("../images/barcelonal-jersey.jpeg"),
        featured: false,
        searchTerms: [
          "barcelona",
          "barca",
          "away",
          "jersey",
          "football",
          "soccer",
        ],
      },
      {
        id: "10",
        name: "Real Madrid Away Jersey",
        price: 89,
        category: "jerseys",
        description: "Official Real Madrid away jersey in dark colors",
        imageUrl: require("../images/real-madrid-jersey.jpeg"),
        featured: false,
        searchTerms: ["real madrid", "madrid", "away", "jersey", "football"],
      },
      {
        id: "11",
        name: "Liverpool FC Away Jersey",
        price: 85,
        category: "jerseys",
        description: "Official Liverpool FC away jersey in alternate colors",
        imageUrl: require("../images/liverpool-jersey.jpeg"),
        featured: true,
        searchTerms: ["liverpool", "lfc", "away", "jersey", "football"],
      },
      {
        id: "12",
        name: "Manchester City Away Jersey",
        price: 89,
        category: "jerseys",
        description: "Official Manchester City away jersey in alternate colors",
        imageUrl: require("../images/man-city-jersey.jpeg"),
        featured: false,
        searchTerms: ["manchester city", "city", "away", "jersey", "football"],
      },
    ];

    if (!query || query.trim() === "") {
      return mockProducts;
    }

    const searchTerm = query.toLowerCase();
    return mockProducts.filter((product) =>
      product.searchTerms.some((term) => term.includes(searchTerm))
    );
  }
}

export default new ProductService();
