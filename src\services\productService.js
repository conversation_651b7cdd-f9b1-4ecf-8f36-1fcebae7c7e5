import { firestore } from "../config/firebase";
import {
  collection,
  getDocs,
  doc,
  getDoc,
  query,
  where,
  limit,
} from "firebase/firestore";

const productsCollection = "products";

class ProductService {
  // Search products by query
  async searchProducts(searchTerm) {
    try {
      if (!searchTerm || searchTerm.trim() === "") {
        // If no query, return all products
        const snapshot = await getDocs(
          query(collection(firestore, productsCollection), limit(20))
        );
        return this._processSnapshot(snapshot);
      }

      // Convert query to lowercase for case-insensitive search
      const searchTermLower = searchTerm.toLowerCase();

      // In a real app, you would use Firestore's full-text search capabilities
      // or integrate with a service like Algolia
      // For this MVP, we'll use a simple query on name and category fields
      const q = query(
        collection(firestore, productsCollection),
        where("searchTerms", "array-contains", searchTermLower),
        limit(20)
      );
      const snapshot = await getDocs(q);

      return this._processSnapshot(snapshot);
    } catch (error) {
      console.error("Error searching products:", error);
      // Fall back to mock products
      return this.getMockProducts(searchTerm);
    }
  }

  // Get product by ID
  async getProductById(productId) {
    try {
      const docRef = doc(firestore, productsCollection, productId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      return {
        id: docSnap.id,
        ...docSnap.data(),
      };
    } catch (error) {
      console.error("Error getting product by ID:", error);
      // Fall back to mock products
      const mockProducts = this.getMockProducts();
      return mockProducts.find((p) => p.id === productId);
    }
  }

  // Get featured products
  async getFeaturedProducts() {
    try {
      // Check if Firebase is properly configured
      if (
        firestore._databaseId &&
        firestore._databaseId.projectId !== "YOUR_PROJECT_ID"
      ) {
        const q = query(
          collection(firestore, productsCollection),
          where("featured", "==", true),
          limit(10)
        );
        const snapshot = await getDocs(q);
        const result = this._processSnapshot(snapshot);

        // If no products found in Firestore, fall back to mock data
        if (result.length === 0) {
          throw new Error("No featured products found in Firestore");
        }

        return result;
      } else {
        // Firebase not properly configured, use mock data
        throw new Error("Firebase not properly configured");
      }
    } catch (error) {
      console.error("Error getting featured products:", error);
      // Fall back to mock products
      const mockProducts = this.getMockProducts();
      return mockProducts.filter((p) => p.featured);
    }
  }

  // Helper method to process Firestore snapshot
  _processSnapshot(snapshot) {
    const products = [];

    snapshot.forEach((doc) => {
      products.push({
        id: doc.id,
        ...doc.data(),
      });
    });

    return products;
  }

  // For demo purposes, if Firestore is not set up yet, return mock products
  getMockProducts(query = "") {
    const mockProducts = [
      {
        id: "1",
        name: "Nike Air Max",
        price: 120,
        category: "shoes",
        description: "Comfortable running shoes with air cushioning",
        imageUrl: require("../images/nike.png"),
        featured: true,
        searchTerms: ["nike", "air", "max", "shoes", "running"],
      },
      {
        id: "2",
        name: "Adidas T-Shirt",
        price: 35,
        category: "shirts",
        description: "Cotton t-shirt with Adidas logo",
        imageUrl: require("../images/adidas.png"),
        featured: true,
        searchTerms: ["adidas", "shirt", "t-shirt", "cotton"],
      },
      {
        id: "3",
        name: "Levi's Jeans",
        price: 60,
        category: "pants",
        description: "Classic blue denim jeans",
        imageUrl: require("../images/levis.png"),
        featured: false,
        searchTerms: ["levis", "jeans", "denim", "pants"],
      },
      {
        id: "4",
        name: "Puma Sneakers",
        price: 85,
        category: "shoes",
        description: "Casual sneakers for everyday wear",
        imageUrl: require("../images/puma.png"),
        featured: false,
        searchTerms: ["puma", "sneakers", "shoes", "casual"],
      },
      {
        id: "5",
        name: "Under Armour Hoodie",
        price: 70,
        category: "shirts",
        description: "Comfortable hoodie for workouts and casual wear",
        imageUrl: require("../images/under-armour.png"),
        featured: true,
        searchTerms: ["under armour", "hoodie", "sweatshirt", "workout"],
      },
      {
        id: "6",
        name: "New Balance 574",
        price: 110,
        category: "shoes",
        description: "Classic lifestyle shoes with excellent comfort",
        imageUrl: require("../images/nike.png"), // Using existing image as fallback
        featured: true,
        searchTerms: ["new balance", "574", "shoes", "sneakers", "running"],
      },
      {
        id: "7",
        name: "Calvin Klein Jeans",
        price: 90,
        category: "pants",
        description: "Premium denim jeans with modern fit",
        imageUrl: require("../images/levis.png"), // Using existing image as fallback
        featured: true,
        searchTerms: ["calvin klein", "jeans", "denim", "pants"],
      },
      {
        id: "8",
        name: "Tommy Hilfiger Polo",
        price: 45,
        category: "shirts",
        description: "Classic polo shirt with embroidered logo",
        imageUrl: require("../images/adidas.png"), // Using existing image as fallback
        featured: false,
        searchTerms: ["tommy hilfiger", "polo", "shirt", "casual"],
      },
      {
        id: "9",
        name: "Reebok Classic",
        price: 75,
        category: "shoes",
        description: "Iconic sneakers with retro design",
        imageUrl: require("../images/puma.png"), // Using existing image as fallback
        featured: false,
        searchTerms: ["reebok", "classic", "shoes", "sneakers", "retro"],
      },
      {
        id: "10",
        name: "Nike Dri-FIT Shorts",
        price: 35,
        category: "pants",
        description: "Moisture-wicking shorts for athletic performance",
        imageUrl: require("../images/nike.png"), // Using existing image as fallback
        featured: false,
        searchTerms: ["nike", "shorts", "dri-fit", "athletic", "workout"],
      },
      {
        id: "11",
        name: "Adidas Ultraboost",
        price: 180,
        category: "shoes",
        description: "Premium running shoes with responsive cushioning",
        imageUrl: require("../images/adidas.png"), // Using existing image as fallback
        featured: true,
        searchTerms: ["adidas", "ultraboost", "running", "shoes", "premium"],
      },
      {
        id: "12",
        name: "Under Armour Backpack",
        price: 55,
        category: "accessories",
        description: "Durable backpack with multiple compartments",
        imageUrl: require("../images/under-armour.png"), // Using existing image as fallback
        featured: false,
        searchTerms: ["under armour", "backpack", "bag", "accessories"],
      },
    ];

    if (!query || query.trim() === "") {
      return mockProducts;
    }

    const searchTerm = query.toLowerCase();
    return mockProducts.filter((product) =>
      product.searchTerms.some((term) => term.includes(searchTerm))
    );
  }
}

export default new ProductService();
