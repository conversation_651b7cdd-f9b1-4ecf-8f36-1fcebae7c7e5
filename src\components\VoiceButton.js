import React, { useEffect, useRef, useState } from 'react';
import { TouchableOpacity, StyleSheet, View, Text, ActivityIndicator, Animated } from 'react-native';
import { useVoice } from '../context/VoiceContext';
import voiceService from '../services/voiceService';

const VoiceButton = ({ minimized = false }) => {
  const { 
    status, 
    isListening, 
    startListening, 
    stopListening, 
    feedbackMessage,
    lastCommand,
    recognizedCommand,
    STATUS
  } = useVoice();

  const [serverStatus, setServerStatus] = useState('checking');
  const [showFeedback, setShowFeedback] = useState(false);

  // Check server status on component mount
  useEffect(() => {
    checkServerStatus();
  }, []);

  // Check server status periodically
  useEffect(() => {
    const interval = setInterval(checkServerStatus, 10000); // Check every 10 seconds
    return () => clearInterval(interval);
  }, []);

  // Show feedback temporarily when there's a new message
  useEffect(() => {
    if (feedbackMessage) {
      setShowFeedback(true);
      const timer = setTimeout(() => {
        setShowFeedback(false);
      }, 5000); // Hide after 5 seconds
      
      return () => clearTimeout(timer);
    }
  }, [feedbackMessage]);

  // Function to check server status
  const checkServerStatus = async () => {
    try {
      setServerStatus('checking');
      await voiceService.checkServerAvailability();
      setServerStatus('online');
    } catch (error) {
      setServerStatus('offline');
    }
  };

  // Animation for the pulsing effect
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  // Start pulsing animation when listening
  useEffect(() => {
    let pulseAnimation;
    let scaleAnimation;
    
    if (status === STATUS.LISTENING) {
      // Pulse animation for the microphone button
      pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      
      // Scale animation for touch feedback
      scaleAnimation = Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]);
      scaleAnimation.start();
    } else {
      pulseAnim.setValue(1);
      scaleAnim.setValue(1);
    }
    
    return () => {
      if (pulseAnimation) {
        pulseAnimation.stop();
      }
      if (scaleAnimation) {
        scaleAnimation.stop();
      }
    };
  }, [status, pulseAnim, scaleAnim]);

  const handlePress = () => {
    if (serverStatus === 'offline') {
      // If server is offline, try to reconnect
      checkServerStatus();
      return;
    }
    
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Determine button color based on status
  const getButtonColor = () => {
    if (serverStatus === 'offline') {
      return '#ff6b6b'; // Red for offline server
    }
    
    if (serverStatus === 'checking') {
      return '#ffcc5c'; // Yellow for checking server
    }
    
    switch (status) {
      case STATUS.LISTENING:
        return '#ff4444';
      case STATUS.PROCESSING:
        return '#ffbb33';
      case STATUS.SPEAKING:
        return '#00C851';
      default:
        return '#6200ee';
    }
  };

  // Get icon based on status
  const getButtonIcon = () => {
    if (serverStatus === 'offline') {
      return '❌';
    }
    
    if (serverStatus === 'checking') {
      return '⏳';
    }
    
    switch (status) {
      case STATUS.LISTENING:
        return '🎤';
      case STATUS.PROCESSING:
        return '⏳';
      case STATUS.SPEAKING:
        return '🔊';
      default:
        return '🎤';
    }
  };

  // Get animation style based on status
  const getAnimationStyle = () => {
    const baseStyle = { transform: [{ scale: pulseAnim }] };
    
    if (status === STATUS.LISTENING) {
      return {
        ...baseStyle,
        shadowColor: '#ff4444',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.5,
        shadowRadius: 10,
        elevation: 8,
      };
    }
    
    return baseStyle;
  };

  // Render minimized button
  if (minimized) {
    return (
      <Animated.View style={getAnimationStyle()}>
        <TouchableOpacity
          style={[styles.buttonMinimized, { backgroundColor: getButtonColor() }]}
          onPress={handlePress}
          activeOpacity={0.7}
        >
          {status === STATUS.PROCESSING || serverStatus === 'checking' ? (
            <ActivityIndicator color="#ffffff" size="small" />
          ) : (
            <Text style={styles.buttonIconMinimized}>{getButtonIcon()}</Text>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <View style={styles.container}>
      {showFeedback && feedbackMessage ? (
        <Animated.View style={styles.feedbackContainer}>
          <Text style={styles.feedbackText}>{feedbackMessage}</Text>
        </Animated.View>
      ) : null}
      
      {recognizedCommand ? (
        <Text style={styles.recognizedText}>{recognizedCommand}</Text>
      ) : null}
      
      <Animated.View style={getAnimationStyle()}>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: getButtonColor() }]}
          onPress={handlePress}
          activeOpacity={0.7}
        >
          {status === STATUS.PROCESSING || serverStatus === 'checking' ? (
            <ActivityIndicator color="#ffffff" size="small" />
          ) : (
            <View style={styles.buttonContent}>
              <Text style={styles.buttonIcon}>{getButtonIcon()}</Text>
              {!minimized && (
                <Text style={styles.buttonText}>
                  {status === STATUS.LISTENING ? 'Listening...' : 'Voice'}
                </Text>
              )}
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
      
      {status === STATUS.LISTENING && (
        <Text style={styles.helpText}>
          Say a command...
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
  },
  button: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  buttonMinimized: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    fontSize: 24,
    color: 'white',
  },
  buttonIconMinimized: {
    fontSize: 18,
    color: 'white',
  },
  buttonText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  feedbackContainer: {
    position: 'absolute',
    bottom: 70,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 20,
    padding: 10,
    maxWidth: 250,
    zIndex: 999,
  },
  feedbackText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
  recognizedText: {
    marginBottom: 5,
    fontSize: 14,
    color: '#6200ee',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  helpText: {
    marginTop: 5,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});

export default VoiceButton; 