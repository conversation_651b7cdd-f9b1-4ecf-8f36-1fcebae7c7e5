import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import SearchResultsScreen from '../screens/SearchResultsScreen';
import ProductDetailScreen from '../screens/ProductDetailScreen';
import CartScreen from '../screens/CartScreen';
import CheckoutScreen from '../screens/CheckoutScreen';
import PaymentScreen from '../screens/PaymentScreen';
import OrderConfirmationScreen from '../screens/OrderConfirmationScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerStyle: {
          backgroundColor: '#6200ee',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerShown: false, // Hide the default header since we're using custom headers
      }}
    >
      <Stack.Screen 
        name="Home" 
        component={HomeScreen} 
      />
      <Stack.Screen 
        name="SearchResults" 
        component={SearchResultsScreen} 
      />
      <Stack.Screen 
        name="ProductDetail" 
        component={ProductDetailScreen} 
      />
      <Stack.Screen 
        name="Cart" 
        component={CartScreen} 
      />
      <Stack.Screen 
        name="Checkout" 
        component={CheckoutScreen} 
      />
      <Stack.Screen 
        name="Payment" 
        component={PaymentScreen} 
      />
      <Stack.Screen 
        name="OrderConfirmation" 
        component={OrderConfirmationScreen} 
      />
    </Stack.Navigator>
  );
};

export default AppNavigator; 