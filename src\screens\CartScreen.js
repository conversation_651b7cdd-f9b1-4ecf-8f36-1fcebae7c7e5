import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from "react-native";
import CartItem from "../components/CartItem";
import VoiceButton from "../components/VoiceButton";
import VoiceTranscriptWrapper from "../components/VoiceTranscriptWrapper";
import cartService from "../services/cartService";
import { useVoice } from "../context/VoiceContext";

const CartScreen = ({ navigation }) => {
  const [cart, setCart] = useState({
    items: [],
    totalItems: 0,
    totalAmount: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { lastCommand } = useVoice();

  // Load cart when component mounts
  useEffect(() => {
    loadCart();
  }, []);

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();

      // Handle checkout command with common speech recognition errors
      if (
        (commandLower.includes("checkout") ||
          commandLower.includes("check out") ||
          commandLower.includes("proceed to check") ||
          commandLower.includes("pay now") ||
          commandLower.includes("place order")) &&
        cart.items.length > 0
      ) {
        handleCheckout();
      }

      // Handle clear cart command
      if (
        commandLower.includes("clear cart") ||
        commandLower.includes("empty cart") ||
        commandLower.includes("remove all")
      ) {
        handleClearCart();
      }

      // Handle back command
      if (
        commandLower.includes("go back") ||
        commandLower.includes("back") ||
        commandLower.includes("return")
      ) {
        handleBackPress();
      }

      // Handle home command
      if (
        commandLower.includes("home") ||
        commandLower.includes("go home") ||
        commandLower.includes("main page")
      ) {
        navigation.navigate("Home");
      }
    }
  }, [lastCommand, cart.items.length, navigation]);

  // Load cart data
  const loadCart = async () => {
    try {
      setLoading(true);
      const cartData = await cartService.getCart();
      setCart(cartData);
    } catch (error) {
      console.error("Error loading cart:", error);
      Alert.alert("Error", "Failed to load cart data.");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    loadCart();
  };

  // Handle remove item
  const handleRemoveItem = async (productId) => {
    try {
      await cartService.removeItem(productId);
      loadCart();
    } catch (error) {
      console.error("Error removing item:", error);
      Alert.alert("Error", "Failed to remove item from cart.");
    }
  };

  // Handle update quantity
  const handleUpdateQuantity = async (productId, quantity) => {
    try {
      await cartService.updateItemQuantity(productId, quantity);
      loadCart();
    } catch (error) {
      console.error("Error updating quantity:", error);
      Alert.alert("Error", "Failed to update item quantity.");
    }
  };

  // Handle checkout
  const handleCheckout = () => {
    if (cart.items.length === 0) {
      Alert.alert(
        "Empty Cart",
        "Please add items to your cart before checkout."
      );
      return;
    }

    navigation.navigate("Checkout", { cart });
  };

  // Handle back navigation
  const handleBackPress = () => {
    navigation.goBack();
  };

  // Handle clear cart
  const handleClearCart = async () => {
    try {
      if (cart.items.length === 0) {
        return;
      }

      Alert.alert(
        "Clear Cart",
        "Are you sure you want to remove all items from your cart?",
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          {
            text: "Clear",
            onPress: async () => {
              await cartService.clearCart();
              loadCart();
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error clearing cart:", error);
      Alert.alert("Error", "Failed to clear cart.");
    }
  };

  // Render empty cart
  const renderEmptyCart = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>Your cart is empty</Text>
      <TouchableOpacity
        style={styles.shopButton}
        onPress={() => navigation.navigate("Home")}
      >
        <Text style={styles.shopButtonText}>Start Shopping</Text>
      </TouchableOpacity>
    </View>
  );

  // Render cart items
  const renderCartItems = () => (
    <>
      <FlatList
        data={cart.items}
        renderItem={({ item }) => (
          <CartItem
            item={item}
            onRemove={handleRemoveItem}
            onUpdateQuantity={handleUpdateQuantity}
          />
        )}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        onRefresh={handleRefresh}
      />

      <View style={styles.summaryContainer}>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Items:</Text>
          <Text style={styles.summaryValue}>{cart.totalItems}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total:</Text>
          <Text style={styles.summaryTotal}>
            GHS {cart.totalAmount.toFixed(2)}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.checkoutButton}
          onPress={handleCheckout}
        >
          <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
        </TouchableOpacity>

        <View style={styles.voiceInstructionsContainer}>
          <Text style={styles.voiceInstructionsText}>
            Say "Checkout" to proceed to payment
          </Text>
        </View>
      </View>
    </>
  );

  return (
    <VoiceTranscriptWrapper position='bottom'>
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor='#1B5E20' barStyle='light-content' />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerText}>Shopping Cart</Text>
        </View>

        {/* Cart Content */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading cart...</Text>
          </View>
        ) : cart.items.length === 0 ? (
          renderEmptyCart()
        ) : (
          renderCartItems()
        )}

        {/* Voice Button */}
        <View style={styles.floatingButtonContainer}>
          <VoiceButton minimized={true} />
        </View>
      </SafeAreaView>
    </VoiceTranscriptWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f8f8",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#1B5E20",
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
  },
  headerText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: "#666",
    marginBottom: 20,
  },
  shopButton: {
    backgroundColor: "#1B5E20",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  shopButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  listContainer: {
    paddingTop: 8,
    paddingBottom: 120,
  },
  summaryContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#fff",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: "#666",
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  summaryTotal: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1B5E20",
  },
  checkoutButton: {
    backgroundColor: "#1B5E20",
    paddingVertical: 12,
    borderRadius: 5,
    alignItems: "center",
    marginTop: 12,
  },
  checkoutButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  voiceInstructionsContainer: {
    alignItems: "center",
    marginTop: 8,
  },
  voiceInstructionsText: {
    fontSize: 12,
    color: "#666",
    fontStyle: "italic",
  },
  floatingButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
    zIndex: 999,
  },
});

export default CartScreen;
