/**
 * Helper script to find your computer's IP address for the speech recognition server
 * Run this with: node find-ip.js
 */

const os = require('os');

function getLocalIPAddress() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        addresses.push({
          name: name,
          address: interface.address
        });
      }
    }
  }
  
  return addresses;
}

console.log('🔍 Finding your computer\'s IP addresses...\n');

const addresses = getLocalIPAddress();

if (addresses.length === 0) {
  console.log('❌ No external IP addresses found.');
  console.log('Make sure you\'re connected to a network.');
} else {
  console.log('✅ Found the following IP addresses:');
  addresses.forEach((addr, index) => {
    console.log(`${index + 1}. ${addr.name}: ${addr.address}`);
  });
  
  console.log('\n📝 Instructions:');
  console.log('1. Choose the IP address that matches your network (usually Wi-Fi or Ethernet)');
  console.log('2. Update src/config/api.js with your chosen IP address');
  console.log('3. Make sure your Python backend server is running on port 5000');
  console.log('4. Test the connection by running the app');
  
  if (addresses.length > 0) {
    console.log(`\n💡 Most likely IP address: ${addresses[0].address}`);
    console.log(`Update this line in src/config/api.js:`);
    console.log(`return "${addresses[0].address}"; // Change this to your computer's actual IP address`);
  }
}

console.log('\n🚀 To start the backend server:');
console.log('cd backend');
console.log('python app.py');
