import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import AppNavigator from './src/navigation/AppNavigator';
import { VoiceProvider } from './src/context/VoiceContext';
import { PaystackProvider } from 'react-native-paystack-webview';

// Create a custom NavigationContainerRef for use in VoiceContext
const navigationRef = React.createRef();

// Helper function to navigate without the navigation prop
// Make it globally available
global.navigate = (name, params) => {
  if (navigationRef.current) {
    navigationRef.current.navigate(name, params);
  }
};

// Helper function to go back without the navigation prop
global.goBack = () => {
  if (navigationRef.current) {
    navigationRef.current.goBack();
  }
};

// Paystack public key (test key)
const PAYSTACK_PUBLIC_KEY = "pk_test_47365b50300d1d3c5d6dd9932b7cecdaa4927b3a";

// Wrap the app with necessary providers
const App = () => {
  return (
    <SafeAreaProvider>
      <StatusBar style="light" backgroundColor="#6200ee" />
      <PaystackProvider
        publicKey={PAYSTACK_PUBLIC_KEY}
        currency="GHS"
        debug={false}
      >
        <NavigationContainer ref={navigationRef}>
          <VoiceProvider>
            <AppNavigator />
          </VoiceProvider>
        </NavigationContainer>
      </PaystackProvider>
    </SafeAreaProvider>
  );
};

export default App;
