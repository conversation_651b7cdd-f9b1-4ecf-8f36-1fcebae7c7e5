import { Audio } from "expo-av";
import * as Speech from "expo-speech";
import { Platform, Alert } from "react-native";
import { determineAction } from "../utils/commandProcessor";
import { API_CONFIG } from "../config/api";

// Voice recording settings optimized for speech
const RECORDING_OPTIONS = {
  android: {
    extension: ".m4a",
    outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
    audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
    sampleRate: 16000, // Optimized for speech
    numberOfChannels: 1, // Mono for speech
    bitRate: 64000, // Lower bitrate for speech
  },
  ios: {
    extension: ".m4a",
    outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
    audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_MEDIUM,
    sampleRate: 16000, // Optimized for speech
    numberOfChannels: 1, // Mono for speech
    bitRate: 64000, // Lower bitrate for speech
    linearPCMBitDepth: 16,
    linearPCMIsBigEndian: false,
    linearPCMIsFloat: false,
  },
};

// Enhanced shopping commands with variations
const SHOPPING_COMMANDS = [
  // Search commands
  "search for shoes",
  "find shoes",
  "show me shoes",
  "look for shoes",
  "search for shirts",
  "find shirts",
  "show me shirts",
  "look for shirts",
  "search for pants",
  "find pants",
  "show me pants",
  "look for pants",
  "search for dress",
  "find dress",
  "show me dress",
  "look for dress",
  "search for jacket",
  "find jacket",
  "show me jacket",
  "look for jacket",
  "search for sneakers",
  "find sneakers",
  "show me sneakers",
  "search for boots",
  "find boots",
  "show me boots",

  // Cart commands
  "add to cart",
  "add item",
  "add this",
  "put in cart",
  "view cart",
  "show cart",
  "open cart",
  "my cart",
  "remove from cart",
  "delete item",
  "remove this",
  "clear cart",
  "empty cart",
  "delete all",

  // Navigation commands
  "checkout",
  "proceed to checkout",
  "buy now",
  "purchase",
  "confirm payment",
  "pay now",
  "complete purchase",
  "finish order",
  "go back",
  "back",
  "previous",
  "return",
  "go home",
  "home",
  "main page",
  "start over",

  // Selection commands
  "select item one",
  "choose first",
  "pick first",
  "first item",
  "select item two",
  "choose second",
  "pick second",
  "second item",
  "select item three",
  "choose third",
  "pick third",
  "third item",

  // Utility commands
  "help",
  "what can I say",
  "commands",
  "instructions",
  "cancel",
  "stop",
  "never mind",
  "abort",
];

class VoiceService {
  constructor() {
    this.recording = null;
    this.isRecording = false;
    this.isSpeaking = false;
    this.lastRecognizedCommand = null;
    this.recognitionResult = null;
    this.recognitionTimeout = null;
    this.isListening = false;
    this.commandHistory = [];
    this.serverAvailable = false;
    this.useServerRecognition = true;

    console.log("VoiceService initialized");
    this.checkServerAvailability();
  }

  // Check if the backend server is available with multiple IP attempts
  async checkServerAvailability() {
    const possibleIPs = [
      "***********", // Your WiFi IP address (detected)
      "********", // Your NordLynx IP address (detected)
      "*************", // Common home network range
      "*************", // Alternative home network range
      "**********", // Alternative network range
      "localhost", // Fallback for web
    ];

    for (const ip of possibleIPs) {
      try {
        const testUrl = `http://${ip}:5000/health`;
        console.log(`Trying to connect to speech server at: ${testUrl}`);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);

        const response = await fetch(testUrl, {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          console.log(`Speech recognition server is available at: ${testUrl}`);
          this.serverAvailable = true;
          // Update the API config to use the working IP
          API_CONFIG.baseUrl = `http://${ip}:5000`;
          return;
        }
      } catch (error) {
        console.log(`Failed to connect to ${ip}:5000 - ${error.message}`);
        continue;
      }
    }

    console.warn(
      "Speech recognition server is not available on any tested IP address"
    );
    console.log("Make sure the Python backend server is running on port 5000");
    console.log("You can start it by running: python backend/app.py");
    this.serverAvailable = false;
  }

  // Initialize audio recording
  async init() {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });
    } catch (error) {
      console.error("Failed to initialize audio:", error);
      throw error;
    }
  }

  // Start recording voice with interactive recognition
  async startRecording() {
    try {
      if (this.isRecording) {
        console.log("Already recording");
        return;
      }

      // Reset recognition result
      this.recognitionResult = null;
      this.isRecording = true;
      this.isListening = true;

      console.log("Starting interactive voice recording");
      await this.init();
      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync(RECORDING_OPTIONS);
      await this.recording.startAsync();
      console.log("Audio recording started - listening for voice input");

      // Set timeout for recording (8 seconds)
      this.recognitionTimeout = setTimeout(() => {
        console.log("Recording timeout");
        this.stopRecording();
      }, 8000);
    } catch (error) {
      console.error("Failed to start recording:", error);
      this.isRecording = false;
      this.isListening = false;
      throw new Error(
        "Unable to start voice recording. Please check microphone permissions."
      );
    }
  }

  // Stop recording and return the audio file URI
  async stopRecording() {
    try {
      if (!this.isRecording) {
        console.log("Not recording");
        return null;
      }

      this.isRecording = false;
      this.isListening = false;

      // Clear timeout
      if (this.recognitionTimeout) {
        clearTimeout(this.recognitionTimeout);
        this.recognitionTimeout = null;
      }

      // Stop audio recording
      if (this.recording) {
        await this.recording.stopAndUnloadAsync();
        const uri = this.recording.getURI();
        this.recording = null;
        console.log("Audio recording stopped, file URI:", uri);
        return uri;
      }

      return null;
    } catch (error) {
      console.error("Failed to stop recording:", error);
      this.isRecording = false;
      this.isListening = false;
      throw error;
    }
  }

  // Process the recorded audio with server or fallback recognition
  async processAudio(audioUri) {
    try {
      console.log("Processing audio file:", audioUri);

      // Try server recognition first if available
      if (this.serverAvailable && this.useServerRecognition) {
        try {
          const serverResult = await this.recognizeWithServer(audioUri);
          console.log("Server recognition result:", serverResult);

          this.recognitionResult = serverResult.text;
          this.lastRecognizedCommand = serverResult.text;
          this.commandHistory.push(serverResult.text);

          return serverResult;
        } catch (error) {
          console.warn(
            "Server recognition failed, falling back to interactive selection:",
            error.message
          );
          this.serverAvailable = false; // Disable server for this session
        }
      }

      // Fallback to interactive command selection
      console.log("Using interactive command selection");
      const command = await this.showCommandSelectionDialog();

      if (command) {
        this.recognitionResult = command;
        this.lastRecognizedCommand = command;
        this.commandHistory.push(command);

        return {
          text: command,
          source: "interactive-selection",
          confidence: 1.0,
        };
      }

      // If no command selected, throw error
      throw new Error("No command selected");
    } catch (error) {
      console.error("Failed to process audio:", error);
      throw error;
    }
  }

  // Show command selection dialog to user
  async showCommandSelectionDialog() {
    return new Promise((resolve) => {
      // Create a list of common commands based on context
      const contextualCommands = this.getContextualCommands();

      // For now, we'll use a simple alert-based selection
      // In a real implementation, you might want to use a modal or picker
      const commandList = contextualCommands
        .map((cmd, index) => `${index + 1}. ${cmd}`)
        .join("\n");

      Alert.alert(
        "Voice Command Recognition",
        `Please select the command you spoke:\n\n${commandList}\n\nOr type a custom command:`,
        [
          ...contextualCommands.map((cmd, index) => ({
            text: `${index + 1}. ${cmd}`,
            onPress: () => resolve(cmd),
          })),
          {
            text: "Custom Command",
            onPress: () => {
              // For simplicity, we'll use a predefined custom command
              // In a real app, you might want to show a text input
              resolve("search for shoes");
            },
          },
          {
            text: "Cancel",
            onPress: () => resolve(null),
            style: "cancel",
          },
        ],
        { cancelable: true, onDismiss: () => resolve(null) }
      );
    });
  }

  // Get contextual commands based on app state and history
  getContextualCommands() {
    const lastCommand = this.lastRecognizedCommand;

    // Define command sequences based on typical user flows
    if (!lastCommand) {
      // First interaction - show common starting commands
      return [
        "search for shoes",
        "search for shirts",
        "search for pants",
        "view cart",
        "help",
      ];
    }

    if (lastCommand.includes("search")) {
      // After search - show selection and cart commands
      return [
        "select item one",
        "select item two",
        "select item three",
        "search for shirts",
        "go back",
      ];
    }

    if (lastCommand.includes("select")) {
      // After selection - show cart and navigation commands
      return [
        "add to cart",
        "view details",
        "select item two",
        "go back",
        "search for pants",
      ];
    }

    if (lastCommand.includes("cart")) {
      // After cart operations - show checkout commands
      return [
        "checkout",
        "remove from cart",
        "clear cart",
        "search for shoes",
        "go back",
      ];
    }

    if (lastCommand.includes("checkout")) {
      // After checkout - show payment commands
      return ["confirm payment", "go back", "view cart", "cancel"];
    }

    // Default commands
    return ["search for shoes", "view cart", "go back", "help", "cancel"];
  }

  // Speak text using text-to-speech
  async speak(text) {
    try {
      if (this.isSpeaking) {
        await Speech.stop();
      }

      this.isSpeaking = true;
      await Speech.speak(text, {
        language: "en-US",
        pitch: 1.0,
        rate: 0.9,
        onDone: () => {
          this.isSpeaking = false;
        },
        onError: (error) => {
          console.error("TTS error:", error);
          this.isSpeaking = false;
        },
      });
    } catch (error) {
      console.error("Failed to speak:", error);
      this.isSpeaking = false;
      throw error;
    }
  }

  // Process voice command and return action
  async processCommand(command) {
    if (!command) {
      return {
        action: "unknown",
        payload: null,
      };
    }

    // Store this command for context in future recognitions
    this.lastRecognizedCommand = command;

    // Use the command processor to determine the action
    const result = determineAction(command);
    return result;
  }

  // Recognize speech using the backend server
  async recognizeWithServer(audioUri) {
    try {
      // Create form data for the request
      const formData = new FormData();

      // Add the audio file to the form data
      formData.append("audio", {
        uri: audioUri,
        name: "recording.m4a",
        type: "audio/m4a",
      });

      // Add language parameter
      formData.append("language", "en-US");

      // Send the request to the server with better error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await fetch(`${API_CONFIG.baseUrl}/recognize`, {
        method: "POST",
        body: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Parse the response
      const result = await response.json();

      if (result.success) {
        return {
          text: result.text,
          source: "server",
          confidence: result.confidence || 0.9,
        };
      } else {
        throw new Error(result.error || "Server recognition failed");
      }
    } catch (error) {
      console.error("Server recognition error:", error);
      throw error;
    }
  }
}

export default new VoiceService();
