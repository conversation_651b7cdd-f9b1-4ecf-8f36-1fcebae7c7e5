import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import ProductCard from "../components/ProductCard";
import VoiceButton from "../components/VoiceButton";
import VoiceTranscriptWrapper from "../components/VoiceTranscriptWrapper";
import productService from "../services/productService";
import { useVoice } from "../context/VoiceContext";

const HomeScreen = ({ navigation }) => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [loading, setLoading] = useState(true);
  const { transcript, lastCommand, navigate } = useVoice();

  // Use the navigate function from VoiceContext if available, otherwise use the navigation prop
  const navigateTo = (screen, params) => {
    if (navigate) {
      navigate(screen, params);
    } else if (navigation) {
      navigation.navigate(screen, params);
    }
  };

  // Load featured products when component mounts
  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        setLoading(true);
        // Try to get products from Firestore, fallback to mock data
        try {
          const products = await productService.getFeaturedProducts();
          if (products && products.length > 0) {
            setFeaturedProducts(products);
          } else {
            throw new Error("No products found in Firestore");
          }
        } catch (error) {
          console.log("Falling back to mock products:", error);
          // Get mock products directly
          const mockProducts = productService.getMockProducts();
          const featured = mockProducts.filter((p) => p.featured);
          console.log("Featured mock products:", featured);
          setFeaturedProducts(featured);
        }
      } catch (error) {
        console.error("Error loading products:", error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();

      // Handle search command
      if (commandLower.includes("search")) {
        const searchTerm = commandLower
          .replace("search for", "")
          .replace("search", "")
          .trim();
        if (searchTerm) {
          handleSearch(searchTerm);

          // Add to recent searches
          setRecentSearches((prev) => {
            // Remove duplicates and limit to 5 recent searches
            const updatedSearches = [
              searchTerm,
              ...prev.filter((s) => s !== searchTerm),
            ].slice(0, 5);
            return updatedSearches;
          });
        }
      }

      // Handle view cart command
      if (
        commandLower.includes("view cart") ||
        commandLower.includes("view my cart")
      ) {
        navigateTo("Cart");
      }
    }
  }, [lastCommand, navigation]);

  // Handle search
  const handleSearch = async (searchTerm) => {
    try {
      const products = await productService.getMockProducts(searchTerm);
      navigateTo("SearchResults", { searchTerm, products });
    } catch (error) {
      console.error("Error searching products:", error);
    }
  };

  // Handle product selection
  const handleProductPress = (product) => {
    navigateTo("ProductDetail", {
      productId: product.id,
      productName: product.name,
      product,
    });
  };

  // Handle cart navigation
  const handleCartPress = () => {
    navigateTo("Cart");
  };

  // Handle recent search press
  const handleRecentSearchPress = (searchTerm) => {
    handleSearch(searchTerm);
  };

  // Render modern header with gradient background
  const renderHeader = () => (
    <LinearGradient
      colors={["#1B5E20", "#2E7D32", "#388E3C"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.header}
    >
      <View style={styles.headerContent}>
        <View style={styles.headerTextContainer}>
          <Text style={styles.welcomeText}>Welcome to</Text>
          <Text style={styles.appName}>FootballVoice</Text>
          <Text style={styles.subtitle}>🏆 Premium Football Jerseys</Text>
        </View>
        <TouchableOpacity style={styles.cartButton} onPress={handleCartPress}>
          <View style={styles.cartIconContainer}>
            <Text style={styles.cartButtonText}>🛒</Text>
            <View style={styles.cartBadge}>
              <Text style={styles.cartBadgeText}>3</Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );

  // Render modern voice instructions with enhanced cards
  const renderVoiceInstructions = () => (
    <View style={styles.voiceInstructionsContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.voiceInstructionsTitle}>🎤 Voice Commands</Text>
        <Text style={styles.voiceInstructionsSubtitle}>
          Speak naturally to shop
        </Text>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.commandsScrollContainer}
      >
        <View style={styles.commandCard}>
          <LinearGradient
            colors={["#E8F5E8", "#F1F8E9"]}
            style={styles.commandCardGradient}
          >
            <View style={styles.commandIconContainer}>
              <Text style={styles.commandIconLarge}>🔍</Text>
            </View>
            <Text style={styles.commandLabel}>Search</Text>
            <Text style={styles.commandExample}>"Find Barcelona jersey"</Text>
            <View style={styles.commandIndicator} />
          </LinearGradient>
        </View>

        <View style={styles.commandCard}>
          <LinearGradient
            colors={["#E8F5E8", "#F1F8E9"]}
            style={styles.commandCardGradient}
          >
            <View style={styles.commandIconContainer}>
              <Text style={styles.commandIconLarge}>🛒</Text>
            </View>
            <Text style={styles.commandLabel}>Cart</Text>
            <Text style={styles.commandExample}>"Show my cart"</Text>
            <View style={styles.commandIndicator} />
          </LinearGradient>
        </View>

        <View style={styles.commandCard}>
          <LinearGradient
            colors={["#E8F5E8", "#F1F8E9"]}
            style={styles.commandCardGradient}
          >
            <View style={styles.commandIconContainer}>
              <Text style={styles.commandIconLarge}>➕</Text>
            </View>
            <Text style={styles.commandLabel}>Add</Text>
            <Text style={styles.commandExample}>"Add to cart"</Text>
            <View style={styles.commandIndicator} />
          </LinearGradient>
        </View>

        <View style={styles.commandCard}>
          <LinearGradient
            colors={["#E8F5E8", "#F1F8E9"]}
            style={styles.commandCardGradient}
          >
            <View style={styles.commandIconContainer}>
              <Text style={styles.commandIconLarge}>💳</Text>
            </View>
            <Text style={styles.commandLabel}>Checkout</Text>
            <Text style={styles.commandExample}>"Proceed to checkout"</Text>
            <View style={styles.commandIndicator} />
          </LinearGradient>
        </View>
      </ScrollView>

      {transcript ? (
        <View style={styles.modernTranscriptContainer}>
          <View style={styles.transcriptHeader}>
            <Text style={styles.transcriptIcon}>🎯</Text>
            <Text style={styles.transcriptTitle}>Last Command</Text>
          </View>
          <Text style={styles.transcriptText}>{transcript}</Text>
        </View>
      ) : null}
    </View>
  );

  // Render recent searches
  const renderRecentSearches = () => {
    if (recentSearches.length === 0) return null;

    return (
      <View style={styles.recentSearchesContainer}>
        <Text style={styles.sectionTitle}>Recent Searches</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.recentSearchesScroll}
        >
          {recentSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              style={styles.recentSearchItem}
              onPress={() => handleRecentSearchPress(search)}
            >
              <Text style={styles.recentSearchIcon}>🔍</Text>
              <Text style={styles.recentSearchText}>{search}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Log featured products count
  useEffect(() => {
    if (featuredProducts) {
      console.log("Featured products count:", featuredProducts.length);
    }
  }, [featuredProducts]);

  return (
    <VoiceTranscriptWrapper position='bottom'>
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor='#6200ee' barStyle='light-content' />

        {renderHeader()}

        {loading ? (
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContentContainer}
          >
            {renderVoiceInstructions()}
            {renderRecentSearches()}
            <View style={styles.loadingContainer}>
              <ActivityIndicator size='large' color='#1B5E20' />
              <Text style={styles.loadingText}>Loading jerseys...</Text>
            </View>
            <View style={styles.bottomPadding} />
          </ScrollView>
        ) : featuredProducts && featuredProducts.length > 0 ? (
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContentContainer}
          >
            {renderVoiceInstructions()}
            {renderRecentSearches()}
            <View style={styles.featuredContainer}>
              <Text style={styles.sectionTitle}>Featured Products</Text>
              {featuredProducts.map((item) => (
                <ProductCard
                  key={item.id}
                  product={item}
                  onPress={handleProductPress}
                />
              ))}
            </View>
            <View style={styles.bottomPadding} />
          </ScrollView>
        ) : (
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContentContainer}
          >
            {renderVoiceInstructions()}
            {renderRecentSearches()}
            <Text style={styles.noProductsText}>
              No featured products available
            </Text>
            <View style={styles.bottomPadding} />
          </ScrollView>
        )}

        <View style={styles.floatingButtonContainer}>
          <VoiceButton minimized={true} />
        </View>
      </SafeAreaView>
    </VoiceTranscriptWrapper>
  );
};

const { width } = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F8F9FA",
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 100, // Extra space for the floating button
  },
  topContentContainer: {
    paddingHorizontal: 20,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerTextContainer: {
    flex: 1,
  },
  welcomeText: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 16,
    fontWeight: "500",
  },
  appName: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
    marginVertical: 4,
    textShadowColor: "rgba(0, 0, 0, 0.3)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 16,
    fontWeight: "500",
  },
  cartButton: {
    position: "relative",
  },
  cartIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  cartButtonText: {
    fontSize: 24,
  },
  cartBadge: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "#FF4444",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#fff",
  },
  cartBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  voiceInstructionsContainer: {
    marginHorizontal: 20,
    marginVertical: 15,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  voiceInstructionsTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1B5E20",
    marginBottom: 4,
  },
  voiceInstructionsSubtitle: {
    fontSize: 16,
    color: "#666",
    fontWeight: "500",
  },
  commandsScrollContainer: {
    paddingHorizontal: 4,
  },
  commandCard: {
    width: width * 0.4,
    marginRight: 16,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#1B5E20",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  commandCardGradient: {
    padding: 20,
    alignItems: "center",
    minHeight: 140,
    justifyContent: "space-between",
  },
  commandIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "rgba(27, 94, 32, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    borderWidth: 2,
    borderColor: "rgba(27, 94, 32, 0.2)",
  },
  commandIconLarge: {
    fontSize: 28,
  },
  commandLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1B5E20",
    textAlign: "center",
    marginBottom: 6,
  },
  commandExample: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
    fontStyle: "italic",
    lineHeight: 16,
  },
  commandIndicator: {
    width: 30,
    height: 3,
    backgroundColor: "#1B5E20",
    borderRadius: 2,
    marginTop: 8,
  },
  modernTranscriptContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: "#1B5E20",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  transcriptHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  transcriptIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  transcriptContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: "rgba(27, 94, 32, 0.1)",
    borderRadius: 5,
  },
  transcriptTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#1B5E20",
  },
  transcriptText: {
    fontSize: 14,
    color: "#333",
    marginTop: 5,
  },
  recentSearchesContainer: {
    margin: 15,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  recentSearchesScroll: {
    flexDirection: "row",
  },
  recentSearchItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    padding: 10,
    borderRadius: 20,
    marginRight: 10,
  },
  recentSearchIcon: {
    fontSize: 16,
    marginRight: 5,
  },
  recentSearchText: {
    fontSize: 14,
    color: "#333",
  },
  featuredContainer: {
    flex: 1,
    margin: 15,
    marginTop: 0,
  },
  productList: {
    paddingBottom: 20, // Reduced padding since we have bottom padding in scrollContentContainer
  },
  loadingContainer: {
    padding: 20,
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    color: "#666",
    fontSize: 16,
  },
  noProductsText: {
    padding: 20,
    color: "#666",
    fontSize: 16,
    textAlign: "center",
  },
  floatingButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
    zIndex: 999,
  },
  bottomPadding: {
    height: 60,
  },
});

export default HomeScreen;
