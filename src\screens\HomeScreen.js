import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Image,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import ProductCard from "../components/ProductCard";
import VoiceButton from "../components/VoiceButton";
import VoiceTranscriptWrapper from "../components/VoiceTranscriptWrapper";
import productService from "../services/productService";
import { useVoice } from "../context/VoiceContext";

const HomeScreen = ({ navigation }) => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [loading, setLoading] = useState(true);
  const { transcript, lastCommand, navigate } = useVoice();

  // Use the navigate function from VoiceContext if available, otherwise use the navigation prop
  const navigateTo = (screen, params) => {
    if (navigate) {
      navigate(screen, params);
    } else if (navigation) {
      navigation.navigate(screen, params);
    }
  };

  // Load featured products when component mounts
  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        setLoading(true);
        // Try to get products from Firestore, fallback to mock data
        try {
          const products = await productService.getFeaturedProducts();
          if (products && products.length > 0) {
            setFeaturedProducts(products);
          } else {
            throw new Error("No products found in Firestore");
          }
        } catch (error) {
          console.log("Falling back to mock products:", error);
          // Get mock products directly
          const mockProducts = productService.getMockProducts();
          const featured = mockProducts.filter((p) => p.featured);
          console.log("Featured mock products:", featured);
          setFeaturedProducts(featured);
        }
      } catch (error) {
        console.error("Error loading products:", error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();

      // Handle search command
      if (commandLower.includes("search")) {
        const searchTerm = commandLower
          .replace("search for", "")
          .replace("search", "")
          .trim();
        if (searchTerm) {
          handleSearch(searchTerm);

          // Add to recent searches
          setRecentSearches((prev) => {
            // Remove duplicates and limit to 5 recent searches
            const updatedSearches = [
              searchTerm,
              ...prev.filter((s) => s !== searchTerm),
            ].slice(0, 5);
            return updatedSearches;
          });
        }
      }

      // Handle view cart command
      if (
        commandLower.includes("view cart") ||
        commandLower.includes("view my cart")
      ) {
        navigateTo("Cart");
      }
    }
  }, [lastCommand, navigation]);

  // Handle search
  const handleSearch = async (searchTerm) => {
    try {
      const products = await productService.getMockProducts(searchTerm);
      navigateTo("SearchResults", { searchTerm, products });
    } catch (error) {
      console.error("Error searching products:", error);
    }
  };

  // Handle product selection
  const handleProductPress = (product) => {
    navigateTo("ProductDetail", {
      productId: product.id,
      productName: product.name,
      product,
    });
  };

  // Handle cart navigation
  const handleCartPress = () => {
    navigateTo("Cart");
  };

  // Handle recent search press
  const handleRecentSearchPress = (searchTerm) => {
    handleSearch(searchTerm);
  };

  // Render header with welcome message and cart button
  const renderHeader = () => (
    <View style={styles.header}>
      <View>
        <Text style={styles.welcomeText}>Welcome to</Text>
        <Text style={styles.appName}>VibeShop</Text>
        <Text style={styles.subtitle}>Voice Shopping Assistant</Text>
      </View>
      <TouchableOpacity style={styles.cartButton} onPress={handleCartPress}>
        <Text style={styles.cartButtonText}>🛒</Text>
      </TouchableOpacity>
    </View>
  );

  // Render voice instructions
  const renderVoiceInstructions = () => (
    <View style={styles.voiceInstructionsContainer}>
      <Text style={styles.voiceInstructionsTitle}>Voice Commands</Text>
      <View style={styles.commandsContainer}>
        <View style={styles.commandCard}>
          <View style={styles.commandIconContainer}>
            <Text style={styles.commandIconLarge}>🔍</Text>
          </View>
          <Text style={styles.commandLabel}>Search</Text>
          <Text style={styles.commandExample}>"Search for shoes"</Text>
        </View>

        <View style={styles.commandCard}>
          <View style={styles.commandIconContainer}>
            <Text style={styles.commandIconLarge}>🛒</Text>
          </View>
          <Text style={styles.commandLabel}>Cart</Text>
          <Text style={styles.commandExample}>"View my cart"</Text>
        </View>

        <View style={styles.commandCard}>
          <View style={styles.commandIconContainer}>
            <Text style={styles.commandIconLarge}>💳</Text>
          </View>
          <Text style={styles.commandLabel}>Checkout</Text>
          <Text style={styles.commandExample}>"Checkout"</Text>
        </View>
      </View>

      {transcript ? (
        <View style={styles.transcriptContainer}>
          <Text style={styles.transcriptTitle}>Last command:</Text>
          <Text style={styles.transcriptText}>{transcript}</Text>
        </View>
      ) : null}
    </View>
  );

  // Render recent searches
  const renderRecentSearches = () => {
    if (recentSearches.length === 0) return null;

    return (
      <View style={styles.recentSearchesContainer}>
        <Text style={styles.sectionTitle}>Recent Searches</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.recentSearchesScroll}
        >
          {recentSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              style={styles.recentSearchItem}
              onPress={() => handleRecentSearchPress(search)}
            >
              <Text style={styles.recentSearchIcon}>🔍</Text>
              <Text style={styles.recentSearchText}>{search}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Log featured products count
  useEffect(() => {
    if (featuredProducts) {
      console.log("Featured products count:", featuredProducts.length);
    }
  }, [featuredProducts]);

  return (
    <VoiceTranscriptWrapper position='bottom'>
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor='#6200ee' barStyle='light-content' />

        {renderHeader()}
        
        {loading ? (
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContentContainer}
          >
            {renderVoiceInstructions()}
            {renderRecentSearches()}
            <View style={styles.loadingContainer}>
              <ActivityIndicator size='large' color='#6200ee' />
              <Text style={styles.loadingText}>Loading products...</Text>
            </View>
            <View style={styles.bottomPadding} />
          </ScrollView>
        ) : featuredProducts && featuredProducts.length > 0 ? (
          <>
            <View style={styles.topContentContainer}>
              {renderVoiceInstructions()}
              {renderRecentSearches()}
            </View>
            <View style={styles.featuredContainer}>
              <Text style={styles.sectionTitle}>Featured Products</Text>
              <FlatList
                data={featuredProducts}
                renderItem={({ item }) => (
                  <ProductCard product={item} onPress={handleProductPress} />
                )}
                keyExtractor={(item) => item.id.toString()}
                horizontal={false}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.productList}
                ListFooterComponent={<View style={styles.bottomPadding} />}
              />
            </View>
          </>
        ) : (
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContentContainer}
          >
            {renderVoiceInstructions()}
            {renderRecentSearches()}
            <Text style={styles.noProductsText}>
              No featured products available
            </Text>
            <View style={styles.bottomPadding} />
          </ScrollView>
        )}

        <View style={styles.floatingButtonContainer}>
          <VoiceButton minimized={true} />
        </View>
      </SafeAreaView>
    </VoiceTranscriptWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 80, // Extra space for the floating button
  },
  topContentContainer: {
    paddingHorizontal: 15,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 15,
    backgroundColor: "#6200ee",
  },
  welcomeText: {
    color: "rgba(255, 255, 255, 0.7)",
    fontSize: 16,
  },
  appName: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
  },
  subtitle: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 14,
    marginTop: 2,
  },
  cartButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  cartButtonText: {
    fontSize: 24,
  },
  voiceInstructionsContainer: {
    margin: 15,
    padding: 15,
    backgroundColor: "#f5f5f5",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  voiceInstructionsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  commandsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
  },
  commandCard: {
    width: "30%",
    alignItems: "center",
    marginBottom: 15,
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  commandIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#e0e0e0",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  commandIconLarge: {
    fontSize: 30,
  },
  commandLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#333",
    textAlign: "center",
    marginBottom: 5,
  },
  commandExample: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  transcriptContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: "rgba(98, 0, 238, 0.1)",
    borderRadius: 5,
  },
  transcriptTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#6200ee",
  },
  transcriptText: {
    fontSize: 14,
    color: "#333",
    marginTop: 5,
  },
  recentSearchesContainer: {
    margin: 15,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  recentSearchesScroll: {
    flexDirection: "row",
  },
  recentSearchItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    padding: 10,
    borderRadius: 20,
    marginRight: 10,
  },
  recentSearchIcon: {
    fontSize: 16,
    marginRight: 5,
  },
  recentSearchText: {
    fontSize: 14,
    color: "#333",
  },
  featuredContainer: {
    flex: 1,
    margin: 15,
    marginTop: 0,
  },
  productList: {
    paddingBottom: 20, // Reduced padding since we have bottom padding in scrollContentContainer
  },
  loadingContainer: {
    padding: 20,
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    color: "#666",
    fontSize: 16,
  },
  noProductsText: {
    padding: 20,
    color: "#666",
    fontSize: 16,
    textAlign: "center",
  },
  floatingButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
    zIndex: 999,
  },
  bottomPadding: {
    height: 60,
  },
});

export default HomeScreen;
