{"name": "footballvoice", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/config-plugins": "^10.1.2", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-voice/voice": "^3.2.4", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "axios": "^1.10.0", "expo": "~53.0.17", "expo-av": "^15.1.7", "expo-file-system": "^18.1.11", "expo-linear-gradient": "^14.1.5", "expo-speech": "^13.1.7", "expo-status-bar": "~2.2.3", "firebase": "^11.10.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-paystack-webview": "^5.0.1", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-native-webview": "^13.15.0", "react-navigation": "^4.4.4", "react-navigation-stack": "^2.10.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}