// Simple plugin that adds voice recognition permissions
module.exports = (config) => {
  // Add iOS permissions
  if (!config.ios) {
    config.ios = {};
  }
  if (!config.ios.infoPlist) {
    config.ios.infoPlist = {};
  }
  
  // Add microphone permission
  config.ios.infoPlist.NSMicrophoneUsageDescription = 
    config.ios.infoPlist.NSMicrophoneUsageDescription || 
    'Allow $(PRODUCT_NAME) to access your microphone';
  
  // Add speech recognition permission
  config.ios.infoPlist.NSSpeechRecognitionUsageDescription = 
    config.ios.infoPlist.NSSpeechRecognitionUsageDescription || 
    'Allow $(PRODUCT_NAME) to access speech recognition';
  
  // Add Android permissions
  if (!config.android) {
    config.android = {};
  }
  if (!config.android.permissions) {
    config.android.permissions = [];
  }
  
  if (!config.android.permissions.includes('android.permission.RECORD_AUDIO')) {
    config.android.permissions.push('android.permission.RECORD_AUDIO');
  }
  
  return config;
}; 