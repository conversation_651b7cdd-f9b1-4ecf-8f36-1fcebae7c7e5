/**
 * VibeShop Backend Connection Test
 * 
 * This script tests the connection to the VibeShop backend server.
 * Run this script with Node.js to verify your backend setup.
 * 
 * Usage:
 * 1. Update the SERVER_URL variable with your backend server IP and port
 * 2. Run: node test_server_connection.js
 */

const http = require('http');
const url = require('url');

// Update this URL with your backend server address
const SERVER_URL = 'http://localhost:5000';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

console.log(`${colors.cyan}VibeShop Backend Connection Test${colors.reset}`);
console.log(`${colors.yellow}Testing connection to: ${SERVER_URL}${colors.reset}`);

// Parse the server URL
const parsedUrl = url.parse(SERVER_URL);

// Test endpoint
const testEndpoint = '/api/test';

// Options for the HTTP request
const options = {
  hostname: parsedUrl.hostname,
  port: parsedUrl.port,
  path: testEndpoint,
  method: 'GET',
  timeout: 5000 // 5 second timeout
};

console.log(`${colors.yellow}Sending request to ${parsedUrl.hostname}:${parsedUrl.port}${testEndpoint}...${colors.reset}`);

// Make the HTTP request
const req = http.request(options, (res) => {
  console.log(`${colors.blue}Response status code: ${res.statusCode}${colors.reset}`);
  
  let data = '';
  
  // Collect response data
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  // Process the complete response
  res.on('end', () => {
    if (res.statusCode === 200) {
      console.log(`${colors.green}Connection successful!${colors.reset}`);
      try {
        const jsonResponse = JSON.parse(data);
        console.log(`${colors.blue}Server response:${colors.reset}`, jsonResponse);
      } catch (e) {
        console.log(`${colors.blue}Server response:${colors.reset}`, data);
      }
    } else {
      console.log(`${colors.red}Connection failed with status code: ${res.statusCode}${colors.reset}`);
      console.log(`${colors.red}Response:${colors.reset}`, data);
    }
    
    console.log(`\n${colors.cyan}Next steps:${colors.reset}`);
    if (res.statusCode === 200) {
      console.log(`${colors.green}1. Your backend server is running correctly.${colors.reset}`);
      console.log(`${colors.green}2. Make sure the BASE_URL in src/config/api.js is set to: ${SERVER_URL}${colors.reset}`);
      console.log(`${colors.green}3. Start the frontend app with: npm start${colors.reset}`);
    } else {
      console.log(`${colors.yellow}1. Check if the backend server is running.${colors.reset}`);
      console.log(`${colors.yellow}2. Verify the server URL: ${SERVER_URL}${colors.reset}`);
      console.log(`${colors.yellow}3. Check if your firewall is blocking the connection.${colors.reset}`);
      console.log(`${colors.yellow}4. Run the backend server with: cd backend && .\\run.bat${colors.reset}`);
    }
  });
});

// Handle request errors
req.on('error', (error) => {
  console.log(`${colors.red}Connection error: ${error.message}${colors.reset}`);
  
  if (error.code === 'ECONNREFUSED') {
    console.log(`${colors.red}The server at ${SERVER_URL} refused the connection.${colors.reset}`);
    console.log(`${colors.yellow}Possible causes:${colors.reset}`);
    console.log(`${colors.yellow}1. The backend server is not running.${colors.reset}`);
    console.log(`${colors.yellow}2. The server is running on a different port.${colors.reset}`);
    console.log(`${colors.yellow}3. The hostname is incorrect.${colors.reset}`);
  } else if (error.code === 'ETIMEDOUT') {
    console.log(`${colors.red}The connection to ${SERVER_URL} timed out.${colors.reset}`);
    console.log(`${colors.yellow}Possible causes:${colors.reset}`);
    console.log(`${colors.yellow}1. The server is not reachable.${colors.reset}`);
    console.log(`${colors.yellow}2. A firewall is blocking the connection.${colors.reset}`);
  }
  
  console.log(`\n${colors.cyan}Troubleshooting steps:${colors.reset}`);
  console.log(`${colors.yellow}1. Check if the backend server is running with: cd backend && python run.py${colors.reset}`);
  console.log(`${colors.yellow}2. Verify your IP address with: ipconfig${colors.reset}`);
  console.log(`${colors.yellow}3. Update the SERVER_URL in this script with your correct IP address.${colors.reset}`);
  console.log(`${colors.yellow}4. Try disabling your firewall temporarily.${colors.reset}`);
});

// Set request timeout
req.on('timeout', () => {
  console.log(`${colors.red}Request timed out after 5 seconds.${colors.reset}`);
  req.abort();
});

// Send the request
req.end();
