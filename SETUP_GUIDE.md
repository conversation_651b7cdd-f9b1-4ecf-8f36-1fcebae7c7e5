# VibeShop - Quick Setup Guide

## ESSENTIAL STEPS (DO THESE FIRST)

### 1. Install Required Tools
- Install [Node.js](https://nodejs.org/) (LTS version)
- Install [Expo CLI](https://docs.expo.dev/get-started/installation/): `npm install -g expo-cli`
- Install [Python](https://www.python.org/downloads/windows/) (3.9 or later) - **CHECK "Add Python to PATH"**
- Install [Git](https://git-scm.com/download/win) (optional, for version control)

### 2. Install Dependencies
```bash
# Frontend dependencies (in project root)
cd path\to\VibeShop
npm install

# Backend dependencies
cd backend
pip install -r requirements.txt
```

### 3. Start Backend Server
```bash
cd path\to\VibeShop\backend
python run.py
# Server will run on http://0.0.0.0:5000
```

### 4. Expo Account Setup
1. Create an Expo account at [expo.dev](https://expo.dev/signup) if you don't have one
2. Install Expo Go app on your phone ([Android](https://play.google.com/store/apps/details?id=host.exp.exponent) / [iOS](https://apps.apple.com/app/expo-go/id982107779))
3. Sign in to the same Expo account on your phone
4. Sign in to Expo CLI on your computer: `expo login`

### 5. Start Frontend
```bash
cd path\to\VibeShop
npx expo start
```
- Scan the QR code with Expo Go app
- OR press 'a' to open in Android emulator

## DETAILED SETUP GUIDE

### Prerequisites
- Windows 10 or later
- At least 8GB RAM
- Internet connection
- Phone with Expo Go app (or Android emulator)

### Firebase Configuration
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Set up Authentication (Email/Password)
4. Create Firestore Database (start in test mode)
5. Update Firebase config in `src/config/firebase.js`:
```javascript
const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_PROJECT_ID.appspot.com",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID"
};
```

### Paystack Configuration
1. Create [Paystack](https://paystack.com/) account
2. Get your test public key
3. Update in `App.js`:
```javascript
const PAYSTACK_PUBLIC_KEY = "YOUR_PAYSTACK_PUBLIC_KEY";
```

### IP Configuration
The backend server runs on `0.0.0.0:5000` by default, making it accessible to all devices on your network.

To connect from your phone or other devices:
1. Find your computer's IP address: Run `ipconfig` in Command Prompt
2. Update in `src/config/api.js`:
```javascript
export const BASE_URL = 'http://YOUR_COMPUTER_IP:5000';
```
3. Make sure your phone and computer are on the same WiFi network

### Testing Backend Connection
```bash
cd path\to\VibeShop
node test_server_connection.js
```

## TROUBLESHOOTING

### App Not Connecting to Backend
- Ensure backend server is running
- Check that your phone and computer are on the same network
- Try using Expo tunnel: `npx expo start --tunnel`
- Verify the IP address in `src/config/api.js` matches your computer's IP

### Python Package Installation Issues
```bash
# Install packages individually if needed
pip install flask
pip install flask-cors
pip install torch
```

### Expo Issues
- Clear cache: `expo r -c`
- Update Expo CLI: `npm install -g expo-cli`
- Try development build: `expo start --dev-client`

### Voice Recognition Issues
- Grant microphone permissions when prompted
- Test with simple commands first
- Ensure backend server is running

## ADDITIONAL RESOURCES
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Paystack Documentation](https://paystack.com/docs/api/) 