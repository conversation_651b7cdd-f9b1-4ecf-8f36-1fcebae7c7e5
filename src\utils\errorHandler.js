/**
 * Utility functions for error handling and logging
 */
import { Alert } from 'react-native';

/**
 * Log an error to the console with additional context
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @param {Object} additionalData - Any additional data to log
 */
export const logError = (error, context, additionalData = {}) => {
  console.error(
    `[ERROR] ${context}: ${error.message}`,
    {
      stack: error.stack,
      ...additionalData
    }
  );
};

/**
 * Show an error alert to the user
 * @param {string} title - Alert title
 * @param {string} message - Alert message
 * @param {Function} onOk - Callback when OK is pressed
 */
export const showErrorAlert = (title, message, onOk = null) => {
  Alert.alert(
    title,
    message,
    [{ text: 'OK', onPress: onOk }],
    { cancelable: false }
  );
};

/**
 * Handle API errors and show appropriate messages
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @param {boolean} showAlert - Whether to show an alert to the user
 * @returns {string} - User-friendly error message
 */
export const handleApiError = (error, context, showAlert = true) => {
  // Log the error
  logError(error, context);
  
  // Default message
  let userMessage = 'Something went wrong. Please try again later.';
  
  // Handle specific error types
  if (error.code) {
    switch (error.code) {
      case 'auth/network-request-failed':
        userMessage = 'Network connection error. Please check your internet connection.';
        break;
      case 'auth/user-not-found':
      case 'auth/wrong-password':
        userMessage = 'Invalid email or password.';
        break;
      case 'permission-denied':
        userMessage = 'You don\'t have permission to perform this action.';
        break;
      default:
        if (error.message) {
          userMessage = error.message;
        }
    }
  }
  
  // Show alert if requested
  if (showAlert) {
    showErrorAlert('Error', userMessage);
  }
  
  return userMessage;
};

/**
 * Create a safe function wrapper that catches errors
 * @param {Function} fn - Function to wrap
 * @param {string} context - Context for error logging
 * @param {boolean} showAlert - Whether to show an alert on error
 * @returns {Function} - Wrapped function
 */
export const createSafeFunction = (fn, context, showAlert = false) => {
  return async (...args) => {
    try {
      return await fn(...args);
    } catch (error) {
      handleApiError(error, context, showAlert);
      throw error;
    }
  };
};

export default {
  logError,
  showErrorAlert,
  handleApiError,
  createSafeFunction,
}; 