import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from "react-native";
import VoiceButton from "../components/VoiceButton";
import VoiceTranscriptWrapper from "../components/VoiceTranscriptWrapper";
import productService from "../services/productService";
import cartService from "../services/cartService";
import { useVoice } from "../context/VoiceContext";

const ProductDetailScreen = ({ route, navigation }) => {
  const { productId, product: initialProduct } = route.params || {};
  const [product, setProduct] = useState(initialProduct || null);
  const [loading, setLoading] = useState(!initialProduct);
  const [addingToCart, setAddingToCart] = useState(false);
  const { lastCommand } = useVoice();

  // Load product details if not provided in navigation params
  useEffect(() => {
    if (!initialProduct && productId) {
      const loadProduct = async () => {
        try {
          setLoading(true);
          // Try to get product from Firestore, fallback to mock data
          try {
            const productData = await productService.getProductById(productId);
            setProduct(productData);
          } catch (error) {
            console.log("Falling back to mock products:", error);
            const mockProducts = productService.getMockProducts();
            const mockProduct = mockProducts.find((p) => p.id === productId);
            setProduct(mockProduct);
          }
        } catch (error) {
          console.error("Error loading product:", error);
        } finally {
          setLoading(false);
        }
      };

      loadProduct();
    }
  }, [productId, initialProduct]);

  // Handle voice commands
  useEffect(() => {
    if (lastCommand && product) {
      const commandLower = lastCommand.toLowerCase();

      // Handle add to cart command with common speech recognition errors
      if (
        commandLower.includes("add to cart") ||
        commandLower.includes("add to cut") ||
        commandLower.includes("add to car") ||
        commandLower.includes("add this") ||
        commandLower.includes("buy this")
      ) {
        handleAddToCart();
      }

      // Handle view cart command with common speech recognition errors
      if (
        commandLower.includes("view cart") ||
        commandLower.includes("view card") ||
        commandLower.includes("view court") ||
        commandLower.includes("see cart") ||
        commandLower.includes("show cart") ||
        commandLower.includes("open cart") ||
        commandLower.includes("go to cart")
      ) {
        handleViewCart();
      }

      // Handle back command
      if (
        commandLower.includes("go back") ||
        commandLower.includes("back") ||
        commandLower.includes("return") ||
        commandLower.includes("previous")
      ) {
        handleBackPress();
      }
    }
  }, [lastCommand, product, navigation]);

  // Handle add to cart button press
  const handleAddToCart = async () => {
    try {
      setAddingToCart(true);
      await cartService.addItem(product);
      Alert.alert("Success", `${product.name} added to cart!`);
    } catch (error) {
      console.error("Error adding to cart:", error);
      Alert.alert("Error", "Failed to add product to cart.");
    } finally {
      setAddingToCart(false);
    }
  };

  // Handle view cart button press
  const handleViewCart = () => {
    navigation.navigate("Cart");
  };

  // Handle back navigation
  const handleBackPress = () => {
    navigation.goBack();
  };

  // Render loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading product details...</Text>
      </SafeAreaView>
    );
  }

  // Render error state if product not found
  if (!product) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>Product not found</Text>
        <TouchableOpacity
          style={styles.homeButton}
          onPress={() => navigation.navigate("Home")}
        >
          <Text style={styles.homeButtonText}>Back to Home</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <VoiceTranscriptWrapper position='bottom'>
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor='#6200ee' barStyle='light-content' />

        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerText} numberOfLines={1}>
            {product.name}
          </Text>
        </View>

        <ScrollView>
          {/* Product Image */}
          <Image
            source={
              typeof product.imageUrl === "string"
                ? { uri: product.imageUrl }
                : product.imageUrl
            }
            style={styles.image}
            resizeMode='cover'
          />

          {/* Product Info */}
          <View style={styles.infoContainer}>
            <Text style={styles.name}>{product.name}</Text>
            <Text style={styles.price}>GHS {product.price.toFixed(2)}</Text>
            <Text style={styles.category}>{product.category}</Text>

            <View style={styles.divider} />

            <Text style={styles.descriptionTitle}>Description</Text>
            <Text style={styles.description}>{product.description}</Text>

            <View style={styles.voiceInstructionsContainer}>
              <Text style={styles.voiceInstructionsTitle}>Voice Commands</Text>
              <Text style={styles.voiceInstructionsText}>
                • Say "Add to cart" to add this product
              </Text>
              <Text style={styles.voiceInstructionsText}>
                • Say "View cart" to see your cart
              </Text>
              <Text style={styles.voiceInstructionsText}>
                • Say "Go back" to return to previous screen
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.addToCartButton}
            onPress={handleAddToCart}
            disabled={addingToCart}
          >
            <Text style={styles.addToCartButtonText}>
              {addingToCart ? "Adding..." : "Add to Cart"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.viewCartButton}
            onPress={handleViewCart}
          >
            <Text style={styles.viewCartButtonText}>View Cart</Text>
          </TouchableOpacity>
        </View>

        {/* Voice Button */}
        <View style={styles.floatingButtonContainer}>
          <VoiceButton minimized={true} />
        </View>
      </SafeAreaView>
    </VoiceTranscriptWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f8f8",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f8f8",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f8f8",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: "#ff6b6b",
    marginBottom: 20,
    textAlign: "center",
  },
  homeButton: {
    backgroundColor: "#6200ee",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  homeButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#6200ee",
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
  },
  headerText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#fff",
    flex: 1,
  },
  image: {
    width: "100%",
    height: 300,
  },
  infoContainer: {
    padding: 16,
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
  },
  name: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  price: {
    fontSize: 20,
    color: "#6200ee",
    fontWeight: "600",
    marginBottom: 8,
  },
  category: {
    fontSize: 16,
    color: "#666",
    textTransform: "capitalize",
    marginBottom: 16,
  },
  divider: {
    height: 1,
    backgroundColor: "#e0e0e0",
    marginVertical: 16,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: "#333",
    lineHeight: 24,
    marginBottom: 16,
  },
  voiceInstructionsContainer: {
    backgroundColor: "#f0f0f0",
    padding: 16,
    borderRadius: 10,
    marginVertical: 16,
  },
  voiceInstructionsTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#6200ee",
  },
  voiceInstructionsText: {
    fontSize: 14,
    marginBottom: 5,
    color: "#333",
  },
  actionContainer: {
    flexDirection: "row",
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  addToCartButton: {
    flex: 2,
    backgroundColor: "#6200ee",
    paddingVertical: 12,
    borderRadius: 5,
    alignItems: "center",
    marginRight: 8,
  },
  addToCartButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  viewCartButton: {
    flex: 1,
    backgroundColor: "#f0f0f0",
    paddingVertical: 12,
    borderRadius: 5,
    alignItems: "center",
  },
  viewCartButtonText: {
    color: "#333",
    fontWeight: "bold",
    fontSize: 16,
  },
  floatingButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
    zIndex: 999,
  },
});

export default ProductDetailScreen;
